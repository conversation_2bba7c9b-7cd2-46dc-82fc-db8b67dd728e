#!/usr/bin/env python3
"""
音频监听工具GUI版本启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from audio_monitor_gui import main
    
    if __name__ == "__main__":
        print("启动音频监听工具GUI版本...")
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖:")
    print("pip install pyaudio numpy PyQt5")
    sys.exit(1)
except Exception as e:
    print(f"程序运行错误: {e}")
    sys.exit(1)
