import uuid
import sys
import json
import os
import re
import requests
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSpinBox, QTextEdit, QMessageBox,
    QTabWidget, QListWidget, QInputDialog, QDialog, QFormLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox, QComboBox
)
from PyQt5.QtCore import Qt, QSettings, QTimer
from PyQt5.QtGui import QFont, QIcon
import random
import string
import base64
import hmac
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 卡密前缀，需要和主程序保持一致
KEY_PREFIX = "HaoHNB"

# Gitee仓库配置
GITEE_TOKEN = "b995afca4f56b84ef2fedaf533e3ce9f"  # Gitee Token
REPO_OWNER = "chen-duohao"  # Gitee用户名
REPO_NAME = "activation_keys"  # 仓库名称

# 设置
SETTINGS_ORG = "YourOrgName"  # 组织名称
SETTINGS_APP = "OBSController"  # 应用名称

# 添加签名密钥（需要和主程序保持一致）
SIGNATURE_KEY = "HaoHNBv2Secret20240301"  # 建议使用更复杂的密钥

# 云端文件名统一为 generated_keys1.json
GENERATED_KEYS_FILE = "generated_keys1.json"
LOCAL_KEYS_FILE = "local_keys.json"  # 移除本地明文卡密存储

# AES加密密钥（16字节，取SIGNATURE_KEY前16位）
AES_KEY = SIGNATURE_KEY[:16].encode('utf-8')

ALL_GENERATED_KEYS_FILE = "all_generated_keys.json"  # 新增加密明文卡密云端存储

def aes_encrypt(plain_text):
    cipher = AES.new(AES_KEY, AES.MODE_ECB)
    ct_bytes = cipher.encrypt(pad(plain_text.encode('utf-8'), AES.block_size))
    return base64.b64encode(ct_bytes).decode('utf-8')

def aes_decrypt(enc_text):
    cipher = AES.new(AES_KEY, AES.MODE_ECB)
    pt = unpad(cipher.decrypt(base64.b64decode(enc_text)), AES.block_size)
    return pt.decode('utf-8')

class ConfigDialog(QDialog):
    """配置对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("配置")
        self.setModal(True)
        
        layout = QFormLayout(self)
        
        # 创建输入框并设置默认值
        self.token_input = QLineEdit(self)
        self.token_input.setText(GITEE_TOKEN)
        layout.addRow("Gitee Token:", self.token_input)
        
        self.repo_owner_input = QLineEdit(self)
        self.repo_owner_input.setText(REPO_OWNER)
        layout.addRow("Gitee用户名:", self.repo_owner_input)
        
        self.repo_name_input = QLineEdit(self)
        self.repo_name_input.setText(REPO_NAME)
        layout.addRow("仓库名称:", self.repo_name_input)
        
        # 添加按钮
        buttons = QHBoxLayout()
        save_btn = QPushButton("保存", self)
        save_btn.clicked.connect(self.accept)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        cancel_btn = QPushButton("取消", self)
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        
        buttons.addWidget(save_btn)
        buttons.addWidget(cancel_btn)
        layout.addRow(buttons)

    def get_values(self):
        return (
            self.token_input.text().strip(),
            self.repo_owner_input.text().strip(),
            self.repo_name_input.text().strip()
        )

class QueryDialog(QDialog):
    """卡密查询对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("卡密查询")
        self.setMinimumWidth(600)  # 加宽一点以适应更多信息
        
        layout = QVBoxLayout(self)
        
        # 输入区域
        input_layout = QHBoxLayout()
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("请输入要查询的卡密")
        query_btn = QPushButton("查询")
        query_btn.clicked.connect(self.query_key)
        input_layout.addWidget(self.key_input)
        input_layout.addWidget(query_btn)
        layout.addLayout(input_layout)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        self.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                font-family: Consolas, Monaco, monospace;
            }
        """)

    def query_key(self):
        """查询卡密信息"""
        key = self.key_input.text().strip()
        if not key:
            QMessageBox.warning(self, "警告", "请输入卡密")
            return
            
        # 验证卡密格式和校验码
        if not self.validate_key(key):
            QMessageBox.warning(self, "警告", "卡密格式不正确或校验码无效")
            return
            
        try:
            headers = {
                'Authorization': f'token {GITEE_TOKEN}',
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            # 获取已生成的卡密哈希列表
            generated_response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/generated_keys1.json',
                headers=headers,
                timeout=10
            )
            
            # 获取作废的卡密列表
            revoked_response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/revoked_keys.json',
                headers=headers,
                timeout=10
            )
            
            if generated_response.status_code == 200:
                # 解析已生成的卡密哈希列表
                generated_content = base64.b64decode(generated_response.json()['content']).decode('utf-8')
                generated_keys = json.loads(generated_content)
                
                # 查找卡密信息（用hash比对）
                hash_key = hashlib.sha256(key.encode()).hexdigest()
                key_info = None
                for info in generated_keys:
                    if info["hash"] == hash_key:
                        key_info = info
                        break
                
                # 检查是否在作废列表中
                is_revoked = False
                if revoked_response.status_code == 200:
                    revoked_content = base64.b64decode(revoked_response.json()['content']).decode('utf-8')
                    revoked_keys = json.loads(revoked_content)
                    is_revoked = key in revoked_keys
                
                if key_info:
                    result = f"=== 卡密信息 ===\n"
                    result += f"卡密: {key}\n"
                    result += f"有效期: {key_info.get('days', '未知')} 天\n"
                    result += f"生成时间: {key_info.get('generated_at', '未知')}\n"
                    result += f"状态: {'已作废' if is_revoked else key_info.get('status', '未知')}\n"
                    
                    # 添加解绑次数显示
                    unbind_count = key_info.get("unbind_count", 0)
                    result += f"解绑次数: {unbind_count} 次\n"
                    if unbind_count >= 3:
                        result += "⚠️ 警告：解绑次数已达到上限，下次解绑将扣除使用时间\n"
                    
                    # 添加机器码显示
                    machine_code = key_info.get("machine_code", "")
                    if machine_code:
                        result += f"\n=== 设备绑定 ===\n"
                        result += f"机器码: {machine_code}\n"
                    else:
                        result += f"\n=== 设备绑定 ===\n"
                        result += "未绑定任何设备\n"
                    
                    # 如果已激活，显示激活信息
                    if key_info.get("status") == "已激活":
                        activation_time = key_info.get("activation_time", "未知")
                        expiry_time = key_info.get("expiry_time", "未知")
                        
                        if activation_time != "未知" and expiry_time != "未知":
                            try:
                                expiry_date = datetime.strptime(expiry_time, "%Y-%m-%d %H:%M:%S")
                                now = datetime.now()
                                if expiry_date > now:
                                    remaining = expiry_date - now
                                    result += f"\n=== 激活状态 ===\n"
                                    result += f"激活时间: {activation_time}\n"
                                    result += f"到期时间: {expiry_time}\n"
                                    result += f"剩余时间: {remaining.days}天 {remaining.seconds//3600}小时\n"
                                else:
                                    result += f"\n=== 激活状态 ===\n"
                                    result += f"已过期\n"
                                    result += f"激活时间: {activation_time}\n"
                                    result += f"过期时间: {expiry_time}\n"
                            except:
                                result += "\n无法解析时间信息\n"
                    else:
                        result += "\n=== 激活状态 ===\n"
                        result += "未激活\n"
                    
                    self.result_text.setText(result)
                else:
                    QMessageBox.warning(self, "警告", "未找到该卡密信息")
            else:
                QMessageBox.warning(self, "错误", "获取卡密信息失败，请检查网络连接")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询时发生错误：{str(e)}")

    def validate_key(self, key):
        """验证卡密格式和签名
        @param key: 完整卡密字符串
        @return: bool 是否有效
        """
        # 验证卡密格式 (包含8位签名)
        pattern = f"^{KEY_PREFIX}([a-f0-9]{{16}})([0-9]{{4}})([a-f0-9]{{4}})([a-f0-9]{{8}})$"
        match = re.match(pattern, key, re.IGNORECASE)
        if not match:
            return False
            
        # 获取各部分
        hex_part = match.group(1)
        days = int(match.group(2))
        check_code = match.group(3)
        signature = match.group(4)
        
        # 验证签名
        data_to_sign = f"{hex_part}{days:04d}{check_code}"
        expected_signature = self.generate_signature(data_to_sign)
        if signature.lower() != expected_signature.lower():
            return False
            
        # 验证校验码
        expected_check_code = self.generate_check_code(hex_part, days)
        return check_code.lower() == expected_check_code.lower()

    def generate_check_code(self, hex_part, days):
        """生成校验码
        @param hex_part: 16位十六进制部分
        @param days: 天数
        @return: 4位十六进制校验码
        """
        # 将天数转换为4位十进制字符串（补零）
        days_str = f"{days:04d}"
        
        # 组合原始数据
        data = f"{hex_part}{days_str}"
        
        # 使用简单的异或运算生成校验码
        check = 0
        for i in range(0, len(data), 4):
            chunk = data[i:i+4]
            # 补齐不足4位的部分
            chunk = chunk.ljust(4, '0')
            # 将十六进制chunk转换为整数并异或
            check ^= int(chunk, 16)
        
        # 返回4位校验码
        return format(check & 0xFFFF, '04x')

    def generate_signature(self, data):
        """使用HMAC-SHA256生成签名
        @param data: 要签名的数据
        @return: 8位十六进制签名
        """
        hmac_obj = hmac.new(
            SIGNATURE_KEY.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        )
        # 取HMAC的前4个字节（8位十六进制）
        return hmac_obj.hexdigest()[:8]

class KeyGeneratorWindow(QMainWindow):
    """
    卡密生成器主窗口
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OBS控制器卡密生成工具")
        self.setMinimumSize(800, 500)
        
        # 设置窗口图标
        try:
            if getattr(sys, 'frozen', False):
                # 打包后的路径
                icon_path = os.path.join(sys._MEIPASS, "obs3.ico")
            else:
                # 开发环境路径
                icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "obs3.ico")
            
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置窗口图标时出错: {str(e)}")
        
        # 加载配置
        self.load_config()
        
        # 加载作废卡密列表
        self.revoked_keys = self.load_revoked_keys()
        
        # 加载已生成的卡密记录
        self.generated_keys = self.load_generated_keys()
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("OBS控制器卡密生成工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 生成卡密标签页
        generate_tab = QWidget()
        generate_layout = QVBoxLayout(generate_tab)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        # 天数输入
        days_layout = QVBoxLayout()
        days_label = QLabel("激活天数：")
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 3650)  # 最多10年
        self.days_spinbox.setValue(30)
        days_layout.addWidget(days_label)
        days_layout.addWidget(self.days_spinbox)
        input_layout.addLayout(days_layout)
        
        # 数量输入
        count_layout = QVBoxLayout()
        count_label = QLabel("生成数量：")
        self.count_spinbox = QSpinBox()
        self.count_spinbox.setRange(1, 100)  # 最多一次生成100个
        self.count_spinbox.setValue(1)
        count_layout.addWidget(count_label)
        count_layout.addWidget(self.count_spinbox)
        input_layout.addLayout(count_layout)
        
        generate_layout.addLayout(input_layout)
        
        # 生成按钮
        self.generate_button = QPushButton("生成卡密")
        self.generate_button.clicked.connect(self.generate_key)
        generate_layout.addWidget(self.generate_button)
        
        # 结果显示区域
        result_label = QLabel("生成的卡密：")
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        generate_layout.addWidget(result_label)
        generate_layout.addWidget(self.result_text)
        
        # 复制按钮
        self.copy_button = QPushButton("复制到剪贴板")
        self.copy_button.clicked.connect(self.copy_to_clipboard)
        generate_layout.addWidget(self.copy_button)
        
        # 已生成卡密标签页
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        # 搜索和筛选区域
        filter_layout = QHBoxLayout()
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入卡密或天数搜索...")
        self.search_input.textChanged.connect(self.filter_keys)
        search_layout.addWidget(self.search_input)
        filter_layout.addLayout(search_layout, stretch=2)  # 搜索框占更多空间
        
        # 状态筛选下拉框
        status_layout = QHBoxLayout()
        status_label = QLabel("激活状态:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "未激活", "已激活", "已过期"])
        self.status_filter.currentTextChanged.connect(self.filter_keys)
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.status_filter)
        filter_layout.addLayout(status_layout)
        
        history_layout.addLayout(filter_layout)
        
        # 卡密表格
        self.keys_table = QTableWidget()
        self.keys_table.setColumnCount(7)  # 设置列数
        self.keys_table.setHorizontalHeaderLabels([
            "卡密", "天数", "生成时间", "激活时间", "剩余时间", "作废", "删除"
        ])
        # 设置表格样式
        self.keys_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.keys_table.horizontalHeader().setStretchLastSection(True)
        self.keys_table.setAlternatingRowColors(True)
        # 连接双击信号
        self.keys_table.cellDoubleClicked.connect(self.copy_key_to_clipboard)
        history_layout.addWidget(self.keys_table)
        
        # 刷新按钮
        refresh_button = QPushButton("刷新列表")
        refresh_button.clicked.connect(self.refresh_history)
        history_layout.addWidget(refresh_button)
        
        # 添加标签页
        tab_widget.addTab(generate_tab, "生成卡密")
        tab_widget.addTab(history_tab, "已生成卡密")
        
        layout.addWidget(tab_widget)
        
        # 初始显示历史记录
        self.refresh_history()
        
        # 创建工具栏
        toolbar = self.addToolBar("工具栏")
        toolbar.setMovable(False)  # 固定工具栏
        
        # 添加配置按钮
        config_action = toolbar.addAction("⚙️ 配置")
        config_action.triggered.connect(self.show_config_dialog)
        toolbar.addSeparator()
        
        # 添加查询按钮
        query_action = toolbar.addAction("🔍 查询卡密")
        query_action.triggered.connect(self.show_query_dialog)
        
        # 设置工具栏样式
        toolbar.setStyleSheet("""
            QToolBar {
                spacing: 10px;
                padding: 5px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
            }
            QToolButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #45a049;
            }
        """)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 12px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QSpinBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
                min-width: 100px;
            }
            QTextEdit, QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
            }
        """)

    def load_config(self):
        """加载配置"""
        global GITEE_TOKEN, REPO_OWNER, REPO_NAME
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                GITEE_TOKEN = config.get('token', '')
                REPO_OWNER = config.get('repo_owner', '')
                REPO_NAME = config.get('repo_name', '')
        except:
            pass

    def save_config(self, token, repo_owner, repo_name):
        """保存配置"""
        global GITEE_TOKEN, REPO_OWNER, REPO_NAME
        GITEE_TOKEN = token
        REPO_OWNER = repo_owner
        REPO_NAME = repo_name
        with open('config.json', 'w') as f:
            json.dump({
                'token': token,
                'repo_owner': repo_owner,
                'repo_name': repo_name
            }, f)

    def show_config_dialog(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            token, repo_owner, repo_name = dialog.get_values()
            self.save_config(token, repo_owner, repo_name)
            QMessageBox.information(self, "成功", "配置已保存")

    def sync_to_cloud(self):
        """同步作废列表到云端"""
        if not GITEE_TOKEN or not REPO_NAME:
            QMessageBox.warning(self, "警告", "请先配置Gitee Token和仓库名称")
            self.show_config_dialog()
            return

        try:
            # 获取文件的 SHA
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/revoked_keys.json',
                params={'access_token': GITEE_TOKEN}
            )
            
            if response.status_code == 200:
                sha = response.json()['sha']
            else:
                print(f"获取文件SHA失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                sha = ''
            
            # 准备更新文件内容
            content = json.dumps(self.revoked_keys, indent=2, ensure_ascii=False)
            content_base64 = base64.b64encode(content.encode('utf-8')).decode('utf-8')
            
            data = {
                'access_token': GITEE_TOKEN,
                'content': content_base64,
                'message': 'Update revoked keys',
                'branch': 'master'
            }
            
            if sha:
                data['sha'] = sha
            
            # 更新文件
            response = requests.put(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/revoked_keys.json',
                json=data
            )
            
            if response.status_code in [200, 201]:
                QMessageBox.information(self, "成功", "作废列表已同步到云端")
            else:
                print(f"同步到Gitee失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                QMessageBox.warning(self, "错误", f"同步失败: {response.status_code}")
                
        except Exception as e:
            print(f"同步时发生错误：{str(e)}")
            QMessageBox.critical(self, "错误", f"同步时发生错误：{str(e)}")

    def load_revoked_keys(self):
        """加载作废卡密列表"""
        try:
            # 从云端获取作废卡密列表
            if GITEE_TOKEN and REPO_NAME:
                headers = {
                    'Authorization': f'token {GITEE_TOKEN}',
                    'Content-Type': 'application/json;charset=UTF-8'
                }
                
                response = requests.get(
                    f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/revoked_keys.json',
                    headers=headers
                )
                
                if response.status_code == 200:
                    content = base64.b64decode(response.json()['content']).decode('utf-8')
                    return json.loads(content)
            return []
        except Exception as e:
            print(f"加载作废卡密列表时出错: {str(e)}")
            return []

    def save_revoked_keys(self):
        """
        保存作废卡密列表
        """
        with open("revoked_keys.json", 'w', encoding='utf-8') as f:
            json.dump(self.revoked_keys, f, indent=2)

    def update_revoked_list(self):
        """
        更新作废卡密列表显示
        """
        self.revoked_list.clear()
        for key in self.revoked_keys:
            self.revoked_list.addItem(key)

    def revoke_key(self):
        """
        作废卡密
        """
        key, ok = QInputDialog.getText(self, "作废卡密", "请输入要作废的卡密：")
        if ok and key:
            key = key.strip()
            if key in self.revoked_keys:
                QMessageBox.warning(self, "警告", "此卡密已经在作废列表中")
                return
            
            # 验证卡密格式
            pattern = f"^{KEY_PREFIX}-[a-f0-9]{{32}}-\\d+$"
            if not re.match(pattern, key, re.IGNORECASE):
                QMessageBox.warning(self, "警告", "卡密格式不正确")
                return
            
            self.revoked_keys.append(key)
            self.save_revoked_keys()
            self.update_revoked_list()
            
            # 自动同步到云端
            self.sync_to_cloud()
            
            QMessageBox.information(self, "成功", "卡密已作废并同步到云端")

    def unrevoke_key(self):
        """
        取消作废卡密
        """
        current_item = self.revoked_list.currentItem()
        if current_item is None:
            QMessageBox.warning(self, "警告", "请先选择要取消作废的卡密")
            return
        
        key = current_item.text()
        self.revoked_keys.remove(key)
        self.save_revoked_keys()
        self.update_revoked_list()
        
        # 自动同步到云端
        self.sync_to_cloud()
        
        QMessageBox.information(self, "成功", "已取消作废此卡密并同步到云端")

    def generate_key(self):
        """
        批量生成卡密并记录
        """
        try:
            days = int(self.days_spinbox.value())
            count = int(self.count_spinbox.value())
            
            if days <= 0:
                QMessageBox.warning(self, "警告", "激活天数必须大于0")
                return
                
            result_text = f"=== 生成卡密成功 ===\n"
            result_text += f"生成数量: {count}\n"
            result_text += f"有效期: {days} 天\n"
            result_text += f"到期日期: {(datetime.now() + timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')}\n"
            result_text += "==================\n\n"
            
            generated_keys = []
            # 只用all_generated_keys.json
            all_keys = self.load_all_generated_keys()
            # 生成多个卡密
            for i in range(count):
                # 生成16位UUID
                uuid_hex = uuid.uuid4().hex[:16]  # 只取前16位
                
                # 生成校验码
                check_code = self.generate_check_code(uuid_hex, days)
                
                # 生成签名（基于UUID+天数+校验码）
                data_to_sign = f"{uuid_hex}{days:04d}{check_code}"
                signature = self.generate_signature(data_to_sign)
                
                # 组合成卡密 (不使用连字符)
                key = f"{KEY_PREFIX}{uuid_hex}{days:04d}{check_code}{signature}"
                result_text += f"卡密 {i+1}: {key}\n"
                
                # 记录生成的卡密信息（只存all_generated_keys.json和云端hash）
                hash_val = hashlib.sha256(key.encode()).hexdigest()
                key_info = {
                    "key": key,  # 明文
                    "hash": hash_val,  # 云端只存hash
                    "days": days,
                    "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "未激活"  # 状态：未激活/已激活/已过期
                }
                generated_keys.append(key)
                # all_generated_keys.json 记录明文
                all_keys.append(key_info.copy())
            # 先加载云端状态
            cloud_keys = self.load_generated_keys()
            # 合并：以all_generated_keys为主，补充云端状态
            merged = []
            for l in all_keys:
                for c in cloud_keys:
                    if l["hash"] == c["hash"]:
                        l.update(c)
                merged.append(l)
            self.generated_keys = merged
            result_text += "\n==================\n"
            
            # 保存到云端
            ok1 = self.save_generated_keys()
            ok2 = self.save_all_generated_keys(all_keys)
            if ok1 and ok2:
                self.result_text.setText(result_text)
                # 复制到剪贴板
                QApplication.clipboard().setText('\n'.join(generated_keys))
                QMessageBox.information(self, "成功", f"已生成 {count} 个卡密并复制到剪贴板")
            else:
                QMessageBox.warning(self, "警告", "卡密已生成但同步到云端失败，请检查网络连接")
                
        except ValueError:
            QMessageBox.warning(self, "警告", "请输入有效的激活天数")

    def generate_check_code(self, hex_part, days):
        """生成校验码
        @param hex_part: 16位十六进制部分
        @param days: 天数
        @return: 4位十六进制校验码
        """
        # 将天数转换为4位十进制字符串（补零）
        days_str = f"{days:04d}"
        
        # 组合原始数据
        data = f"{hex_part}{days_str}"
        
        # 使用简单的异或运算生成校验码
        check = 0
        for i in range(0, len(data), 4):
            chunk = data[i:i+4]
            # 补齐不足4位的部分
            chunk = chunk.ljust(4, '0')
            # 将十六进制chunk转换为整数并异或
            check ^= int(chunk, 16)
        
        # 返回4位校验码
        return format(check & 0xFFFF, '04x')

    def generate_signature(self, data):
        """使用HMAC-SHA256生成签名
        @param data: 要签名的数据
        @return: 8位十六进制签名
        """
        hmac_obj = hmac.new(
            SIGNATURE_KEY.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        )
        # 取HMAC的前4个字节（8位十六进制）
        return hmac_obj.hexdigest()[:8]

    def generate_random_key(self):
        """生成随机卡密"""
        # 生成一个随机的16位字符串，包含数字和大写字母
        chars = string.ascii_uppercase + string.digits
        key = ''.join(random.choice(chars) for _ in range(16))
        # 每4个字符插入一个连字符
        return '-'.join([key[i:i+4] for i in range(0, 16, 4)])

    def copy_to_clipboard(self):
        """
        将生成的卡密复制到剪贴板
        单个卡密时复制完整信息，多个卡密时只复制卡密内容
        """
        clipboard = QApplication.clipboard()
        text = self.result_text.toPlainText()
        if text:
            # 提取所有卡密
            keys = []
            lines = text.split('\n')
            generated_count = None
            
            # 获取生成数量
            for line in lines:
                if line.startswith("生成数量:"):
                    generated_count = int(line.split(": ")[1])
                    break
            
            # 提取卡密
            for line in lines:
                if line.startswith("卡密"):
                    key = line.split(": ")[1]
                    keys.append(key)
            
            if keys:
                if generated_count == 1:
                    # 单个卡密时复制完整信息
                    clipboard_text = "生成的卡密:\n"
                    clipboard_text += "=== 生成卡密成功 ===\n"
                    
                    # 提取并添加生成信息
                    for line in lines:
                        if line.startswith("生成数量:") or line.startswith("有效期:") or line.startswith("到期日期:"):
                            clipboard_text += line + "\n"
                    
                    clipboard_text += "==================\n\n"
                    clipboard_text += f"卡密 1: {keys[0]}\n"
                    clipboard_text += "\n==================\n"
                else:
                    # 多个卡密时只复制卡密内容
                    clipboard_text = '\n'.join(keys)
                
                clipboard.setText(clipboard_text)
                QMessageBox.information(self, "成功", f"已成功复制 {len(keys)} 个卡密到剪贴板")
            else:
                QMessageBox.warning(self, "警告", "未找到可复制的卡密")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的内容")

    def show_query_dialog(self):
        """显示卡密查询对话框"""
        dialog = QueryDialog(self)
        dialog.exec_()

    def load_generated_keys(self):
        """加载已生成的卡密记录（本地依然保留明文key，云端只下发hash）"""
        try:
            # 从云端获取已生成的卡密列表
            if GITEE_TOKEN and REPO_NAME:
                headers = {
                    'Authorization': f'token {GITEE_TOKEN}',
                    'Content-Type': 'application/json;charset=UTF-8'
                }
                
                response = requests.get(
                    f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/generated_keys1.json',
                    headers=headers
                )
                
                if response.status_code == 200:
                    content = base64.b64decode(response.json()['content']).decode('utf-8')
                    cloud_keys = json.loads(content)
                    # 本地只用hash做比对，明文key只在本地生成时可见
                    return cloud_keys
            return []
        except Exception as e:
            print(f"加载生成的卡密列表时出错: {str(e)}")
            return []

    def save_generated_keys(self):
        """保存已生成的卡密记录到云端（只上传hash，不上传明文key）"""
        if not GITEE_TOKEN or not REPO_NAME:
            QMessageBox.warning(self, "警告", "请先配置Gitee Token和仓库名称")
            self.show_config_dialog()
            return False

        try:
            # 获取文件的 SHA
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                params={'access_token': GITEE_TOKEN}
            )
            
            if response.status_code == 200:
                sha = response.json()['sha']
            else:
                print(f"获取文件SHA失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                sha = ''
            
            # 只上传 hash 相关字段
            cloud_keys = []
            for k in self.generated_keys:
                cloud_keys.append({
                    "hash": k["hash"],
                    "days": k["days"],
                    "generated_at": k["generated_at"],
                    "status": k.get("status", "未激活"),
                    # 其它激活相关字段可按需保留
                    "activation_time": k.get("activation_time"),
                    "expiry_time": k.get("expiry_time"),
                    "machine_code": k.get("machine_code"),
                    "unbind_count": k.get("unbind_count", 0)
                })
            
            content = json.dumps(cloud_keys, indent=2, ensure_ascii=False)
            content_base64 = base64.b64encode(content.encode('utf-8')).decode('utf-8')
            
            data = {
                'access_token': GITEE_TOKEN,
                'content': content_base64,
                'message': 'Update generated keys',
                'branch': 'master'
            }
            
            if sha:
                data['sha'] = sha
            
            # 更新文件
            response = requests.put(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                json=data
            )
            
            if response.status_code not in [200, 201]:
                print(f"同步到Gitee失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
            
            return True
        except Exception as e:
            print(f"同步到Gitee时出错: {str(e)}")
            return False

    def copy_key_to_clipboard(self, row, column):
        """双击复制卡密到剪贴板"""
        # 只有点击卡密列（第0列）时才复制
        if column == 0:
            item = self.keys_table.item(row, column)
            if item:
                key = item.text()
                QApplication.clipboard().setText(key)
                # 显示提示消息
                QMessageBox.information(self, "复制成功", f"卡密 {key} 已复制到剪贴板")

    def filter_keys(self):
        """根据搜索条件和状态过滤并显示卡密"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()
        
        # 清空表格
        self.keys_table.setRowCount(0)
        
        # 加载作废卡密列表
        revoked_keys = self.load_revoked_keys()
        
        row = 0
        for key_info in self.generated_keys:
            # 只显示本地生成的明文key
            key = key_info.get("key", "")
            days = str(key_info.get("days", ""))
            generated_at = key_info.get("generated_at", "")
            
            # 检查激活状态匹配
            current_status = key_info.get("status", "未激活")
            if status_filter != "全部":
                if status_filter == "已激活" and current_status != "已激活":
                    continue
                elif status_filter == "未激活" and current_status != "未激活":
                    continue
                elif status_filter == "已过期":
                    if current_status != "已激活":
                        continue
                    # 检查是否过期
                    if "expiry_time" in key_info:
                        try:
                            expiry_time = datetime.strptime(key_info["expiry_time"], "%Y-%m-%d %H:%M:%S")
                            if expiry_time > datetime.now():
                                continue
                        except:
                            continue
                    else:
                        continue
            
            # 检查是否匹配搜索条件
            if search_text and not (search_text in key.lower() or search_text in days):
                continue
            
            self.keys_table.insertRow(row)
            
            # 添加卡密信息，并设置提示文本
            key_item = QTableWidgetItem(key)
            key_item.setToolTip("双击复制卡密")
            self.keys_table.setItem(row, 0, key_item)
            self.keys_table.setItem(row, 1, QTableWidgetItem(days))
            self.keys_table.setItem(row, 2, QTableWidgetItem(generated_at))
            
            # 检查激活状态
            if current_status == "已激活":
                if "activation_time" in key_info and "expiry_time" in key_info:
                    activation_time = datetime.strptime(key_info["activation_time"], "%Y-%m-%d %H:%M:%S")
                    expiry_time = datetime.strptime(key_info["expiry_time"], "%Y-%m-%d %H:%M:%S")
                    now = datetime.now()
                    
                    self.keys_table.setItem(row, 3, QTableWidgetItem(
                        activation_time.strftime("%Y-%m-%d %H:%M:%S")
                    ))
                    
                    if expiry_time > now:
                        remaining = expiry_time - now
                        self.keys_table.setItem(row, 4, QTableWidgetItem(
                            f"{remaining.days}天 {remaining.seconds//3600}小时"
                        ))
                    else:
                        self.keys_table.setItem(row, 4, QTableWidgetItem("已过期"))
                else:
                    self.keys_table.setItem(row, 3, QTableWidgetItem("已激活"))
                    self.keys_table.setItem(row, 4, QTableWidgetItem("--"))
            else:
                self.keys_table.setItem(row, 3, QTableWidgetItem("未激活"))
                self.keys_table.setItem(row, 4, QTableWidgetItem("--"))
            
            # 添加作废复选框
            revoke_checkbox = QCheckBox()
            revoke_checkbox.setChecked(key in revoked_keys)
            revoke_checkbox.stateChanged.connect(lambda state, k=key: self.toggle_revoke(k, state))
            self.keys_table.setCellWidget(row, 5, revoke_checkbox)
            
            # 添加删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet("background-color: #ff4444; color: white;")
            delete_btn.clicked.connect(lambda _, k=key: self.delete_key(k))
            self.keys_table.setCellWidget(row, 6, delete_btn)
            
            row += 1
    
    def toggle_revoke(self, key, state):
        """切换卡密的作废状态"""
        try:
            if state == Qt.Checked:
                if key not in self.revoked_keys:
                    self.revoked_keys.append(key)
            else:
                if key in self.revoked_keys:
                    self.revoked_keys.remove(key)
            
            # 保存并同步到云端
            self.save_revoked_keys()
            self.sync_to_cloud()
            
            # 刷新显示
            self.refresh_history()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新作废状态时发生错误：{str(e)}")
    
    def delete_key(self, key):
        """删除指定的卡密"""
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除这个卡密吗？删除后将无法恢复，且该卡密将无法继续使用。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 只用all_generated_keys.json
                self.generated_keys = [k for k in self.generated_keys if k["key"] != key]
                # 从作废列表中删除（如果存在）
                if key in self.revoked_keys:
                    self.revoked_keys.remove(key)
                # all_generated_keys.json 也要删除
                all_keys = self.load_all_generated_keys()
                all_keys = [k for k in all_keys if k["key"] != key]
                # 保存并同步到云端
                self.save_generated_keys()
                self.save_all_generated_keys(all_keys)
                self.save_revoked_keys()
                self.sync_to_cloud()
                # 刷新显示
                self.refresh_history()
                QMessageBox.information(self, "成功", "卡密已成功删除")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除卡密时发生错误：{str(e)}")

    def refresh_history(self):
        """刷新历史记录显示"""
        # 只用all_generated_keys.json解密明文
        all_keys = self.load_all_generated_keys()
        self.generated_keys = all_keys if all_keys else []
        # 再加载云端hash及状态
        cloud_keys = self.load_generated_keys()
        # 合并：以all_generated_keys为主，补充云端状态
        merged = []
        for l in self.generated_keys:
            for c in cloud_keys:
                if l["hash"] == c["hash"]:
                    l.update(c)
            merged.append(l)
        self.generated_keys = merged
        self.filter_keys()

    def load_all_generated_keys(self):
        """加载加密明文卡密记录（all_generated_keys.json）"""
        try:
            if GITEE_TOKEN and REPO_NAME:
                headers = {
                    'Authorization': f'token {GITEE_TOKEN}',
                    'Content-Type': 'application/json;charset=UTF-8'
                }
                response = requests.get(
                    f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{ALL_GENERATED_KEYS_FILE}',
                    headers=headers
                )
                if response.status_code == 200:
                    content = base64.b64decode(response.json()['content']).decode('utf-8')
                    enc_keys = json.loads(content)
                    # 解密明文
                    dec_keys = []
                    for k in enc_keys:
                        try:
                            dec = json.loads(aes_decrypt(k["enc_data"]))
                            dec_keys.append(dec)
                        except Exception as e:
                            print(f"解密卡密失败: {e}")
                    return dec_keys
            return []
        except Exception as e:
            print(f"加载加密明文卡密时出错: {str(e)}")
            return []

    def save_all_generated_keys(self, keys):
        """保存加密明文卡密到 all_generated_keys.json 云端"""
        if not GITEE_TOKEN or not REPO_NAME:
            QMessageBox.warning(self, "警告", "请先配置Gitee Token和仓库名称")
            self.show_config_dialog()
            return False
        try:
            # 获取文件的 SHA
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{ALL_GENERATED_KEYS_FILE}',
                params={'access_token': GITEE_TOKEN}
            )
            if response.status_code == 200:
                sha = response.json()['sha']
            else:
                print(f"获取文件SHA失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                sha = ''
            # 加密明文卡密
            enc_keys = []
            for k in keys:
                enc = aes_encrypt(json.dumps(k, ensure_ascii=False))
                enc_keys.append({"enc_data": enc})
            content = json.dumps(enc_keys, indent=2, ensure_ascii=False)
            content_base64 = base64.b64encode(content.encode('utf-8')).decode('utf-8')
            data = {
                'access_token': GITEE_TOKEN,
                'content': content_base64,
                'message': 'Update all generated keys',
                'branch': 'master'
            }
            if sha:
                data['sha'] = sha
            response = requests.put(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{ALL_GENERATED_KEYS_FILE}',
                json=data
            )
            if response.status_code not in [200, 201]:
                print(f"同步到Gitee失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
            return True
        except Exception as e:
            print(f"同步到Gitee时出错: {str(e)}")
            return False

def main():
    """
    主函数，启动GUI应用程序
    """
    # 启动时自动删除本地 local_keys.json 文件（如果存在）
    try:
        local_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "local_keys.json")
        if os.path.exists(local_file):
            os.remove(local_file)
    except Exception as e:
        print(f"删除本地 local_keys.json 时出错: {e}")
    app = QApplication(sys.argv)
    
    # 设置应用程序图标（会同时影响窗口图标和任务栏图标）
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 如果是打包后的exe，需要从不同路径获取图标
        if getattr(sys, 'frozen', False):
            # 打包后的路径
            icon_path = os.path.join(sys._MEIPASS, "obs3.ico")
        else:
            # 开发环境路径
            icon_path = os.path.join(current_dir, "obs3.ico")
            
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
        else:
            print(f"警告：找不到图标文件: {icon_path}")
    except Exception as e:
        print(f"设置图标时出错: {str(e)}")
    
    window = KeyGeneratorWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 