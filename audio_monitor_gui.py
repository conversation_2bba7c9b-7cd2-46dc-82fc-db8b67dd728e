#!/usr/bin/env python3
"""
音频监听工具GUI版本
参考闪避功能，完善音频输入通道检测功能
"""

import sys
import os
import time
import threading
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QComboBox, QProgressBar, QTextEdit, QGroupBox,
    QFormLayout, QSpinBox, QDoubleSpinBox, QCheckBox, QMessageBox,
    QTabWidget, QListWidget, QListWidgetItem, QSlider
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QIcon
import numpy as np

# 尝试导入音频相关库
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("警告: PyAudio 未安装，音频监听功能将受限")

try:
    from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio
    QT_AUDIO_AVAILABLE = True
except ImportError:
    QT_AUDIO_AVAILABLE = False
    print("警告: PyQt5.QtMultimedia 未安装，设备检测功能将受限")


class AudioMonitorThread(QThread):
    """音频监听线程"""
    audio_level_changed = pyqtSignal(str, float, float)  # 设备名, 音量, dB值
    error_occurred = pyqtSignal(str)
    
    def __init__(self, device_info, threshold_db=-30.0):
        super().__init__()
        self.device_info = device_info
        self.threshold_db = threshold_db
        self.is_running = False
        self.stream = None
        self.pyaudio_instance = None
        
    def run(self):
        """运行音频监听"""
        if not PYAUDIO_AVAILABLE:
            self.error_occurred.emit("PyAudio 未安装，无法进行音频监听")
            return
            
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            self.is_running = True
            
            # 配置音频流参数
            chunk_size = 1024
            sample_rate = 44100
            
            # 打开音频流
            self.stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=sample_rate,
                input=True,
                input_device_index=self.device_info.get('index'),
                frames_per_buffer=chunk_size
            )
            
            print(f"开始监听设备: {self.device_info.get('name', '未知设备')}")
            
            while self.is_running:
                try:
                    # 读取音频数据
                    data = self.stream.read(chunk_size, exception_on_overflow=False)
                    
                    # 转换为numpy数组
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    # 计算音量
                    volume = np.sqrt(np.mean(audio_data**2))
                    
                    # 转换为dB
                    if volume > 0:
                        db_level = 20 * np.log10(volume / 32767.0)
                    else:
                        db_level = -80.0
                    
                    # 发送信号
                    device_name = self.device_info.get('name', '未知设备')
                    self.audio_level_changed.emit(device_name, volume, db_level)
                    
                    # 短暂休眠
                    self.msleep(50)  # 50ms间隔
                    
                except Exception as e:
                    if self.is_running:
                        self.error_occurred.emit(f"音频读取错误: {str(e)}")
                    break
                    
        except Exception as e:
            self.error_occurred.emit(f"音频流初始化失败: {str(e)}")
        finally:
            self.cleanup()
    
    def stop(self):
        """停止监听"""
        self.is_running = False
        
    def cleanup(self):
        """清理资源"""
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
            except:
                pass
            self.stream = None
            
        if self.pyaudio_instance:
            try:
                self.pyaudio_instance.terminate()
            except:
                pass
            self.pyaudio_instance = None


class AudioMonitorGUI(QMainWindow):
    """音频监听工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎤 音频监听工具 - 输入通道检测")
        self.setMinimumSize(800, 600)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 初始化变量
        self.audio_devices = []
        self.monitor_threads = {}
        self.device_widgets = {}
        
        # 创建界面
        self.init_ui()
        
        # 刷新设备列表
        self.refresh_audio_devices()
        
        # 应用样式
        self.apply_styles()
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "obs2.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置窗口图标失败: {e}")
    
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("🎤 音频监听工具 - 输入通道检测")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 设备检测标签页
        device_tab = self.create_device_tab()
        tab_widget.addTab(device_tab, "🔍 设备检测")
        
        # 监听控制标签页
        monitor_tab = self.create_monitor_tab()
        tab_widget.addTab(monitor_tab, "🎯 监听控制")
        
        # 日志标签页
        log_tab = self.create_log_tab()
        tab_widget.addTab(log_tab, "📝 监听日志")
        
        main_layout.addWidget(tab_widget)
        
        # 底部控制按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新设备")
        self.refresh_btn.clicked.connect(self.refresh_audio_devices)
        button_layout.addWidget(self.refresh_btn)
        
        self.start_all_btn = QPushButton("▶️ 开始全部监听")
        self.start_all_btn.clicked.connect(self.start_all_monitoring)
        button_layout.addWidget(self.start_all_btn)
        
        self.stop_all_btn = QPushButton("⏹️ 停止全部监听")
        self.stop_all_btn.clicked.connect(self.stop_all_monitoring)
        button_layout.addWidget(self.stop_all_btn)
        
        button_layout.addStretch()
        
        main_layout.addLayout(button_layout)
    
    def create_device_tab(self):
        """创建设备检测标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 设备状态组
        status_group = QGroupBox("📊 设备状态")
        status_layout = QVBoxLayout(status_group)
        
        # 设备列表
        self.device_list_widget = QListWidget()
        status_layout.addWidget(self.device_list_widget)
        
        layout.addWidget(status_group)
        
        # 检测参数组
        params_group = QGroupBox("⚙️ 检测参数")
        params_layout = QFormLayout(params_group)
        
        # 阈值设置
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-80.0, 0.0)
        self.threshold_spin.setValue(-30.0)
        self.threshold_spin.setSuffix(" dB")
        params_layout.addRow("触发阈值:", self.threshold_spin)
        
        # 更新间隔
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(10, 1000)
        self.interval_spin.setValue(50)
        self.interval_spin.setSuffix(" ms")
        params_layout.addRow("更新间隔:", self.interval_spin)
        
        layout.addWidget(params_group)
        
        return tab

    def create_monitor_tab(self):
        """创建监听控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 监听控制组
        control_group = QGroupBox("🎯 监听控制")
        control_layout = QVBoxLayout(control_group)

        # 设备选择
        device_select_layout = QHBoxLayout()
        device_select_layout.addWidget(QLabel("选择设备:"))

        self.device_combo = QComboBox()
        device_select_layout.addWidget(self.device_combo)

        self.monitor_btn = QPushButton("开始监听")
        self.monitor_btn.clicked.connect(self.toggle_device_monitoring)
        device_select_layout.addWidget(self.monitor_btn)

        control_layout.addLayout(device_select_layout)

        # 音量显示
        volume_layout = QVBoxLayout()

        self.volume_label = QLabel("音量: 0.0 dB")
        self.volume_label.setAlignment(Qt.AlignCenter)
        volume_layout.addWidget(self.volume_label)

        self.volume_bar = QProgressBar()
        self.volume_bar.setRange(0, 100)
        self.volume_bar.setValue(0)
        volume_layout.addWidget(self.volume_bar)

        # 音量滑块
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(-80, 0)
        self.volume_slider.setValue(-30)
        self.volume_slider.setEnabled(False)
        volume_layout.addWidget(self.volume_slider)

        control_layout.addLayout(volume_layout)

        layout.addWidget(control_group)

        # 闪避设置组（参考主程序的闪避功能）
        ducking_group = QGroupBox("🎯 闪避设置")
        ducking_layout = QFormLayout(ducking_group)

        # 启用闪避
        self.ducking_enabled = QCheckBox("启用音频闪避")
        ducking_layout.addRow("", self.ducking_enabled)

        # 闪避音量
        self.ducking_volume_spin = QDoubleSpinBox()
        self.ducking_volume_spin.setRange(0.0, 1.0)
        self.ducking_volume_spin.setValue(0.03)
        self.ducking_volume_spin.setSingleStep(0.01)
        ducking_layout.addRow("闪避音量:", self.ducking_volume_spin)

        # 保持时间
        self.hold_time_spin = QDoubleSpinBox()
        self.hold_time_spin.setRange(0.1, 10.0)
        self.hold_time_spin.setValue(0.5)
        self.hold_time_spin.setSuffix(" 秒")
        ducking_layout.addRow("保持时间:", self.hold_time_spin)

        layout.addWidget(ducking_group)

        return tab

    def create_log_tab(self):
        """创建日志标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 日志显示
        log_group = QGroupBox("📝 监听日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 注意：QTextEdit没有setMaximumBlockCount方法，这是QPlainTextEdit的方法
        log_layout.addWidget(self.log_text)

        # 日志控制
        log_control_layout = QHBoxLayout()

        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(self.clear_log_btn)

        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        log_control_layout.addWidget(self.auto_scroll_check)

        log_control_layout.addStretch()

        log_layout.addLayout(log_control_layout)
        layout.addWidget(log_group)

        return tab

    def refresh_audio_devices(self):
        """刷新音频设备列表"""
        self.log_message("🔄 开始刷新音频设备列表...")

        self.audio_devices.clear()
        self.device_combo.clear()
        self.device_list_widget.clear()

        # 方法1: 使用PyAudio获取设备
        if PYAUDIO_AVAILABLE:
            try:
                p = pyaudio.PyAudio()
                device_count = p.get_device_count()

                for i in range(device_count):
                    try:
                        device_info = p.get_device_info_by_index(i)

                        # 只获取输入设备
                        if device_info['maxInputChannels'] > 0:
                            device_data = {
                                'index': i,
                                'name': device_info['name'],
                                'channels': device_info['maxInputChannels'],
                                'sample_rate': int(device_info['defaultSampleRate']),
                                'api': 'PyAudio'
                            }

                            self.audio_devices.append(device_data)

                            # 添加到下拉框
                            display_name = f"{device_data['name']} ({device_data['channels']}ch)"
                            self.device_combo.addItem(display_name, device_data)

                            # 添加到列表
                            item = QListWidgetItem(f"🎤 {display_name}")
                            item.setData(Qt.UserRole, device_data)
                            self.device_list_widget.addItem(item)

                    except Exception as e:
                        print(f"获取设备 {i} 信息失败: {e}")
                        continue

                p.terminate()
                self.log_message(f"✅ PyAudio 检测到 {len(self.audio_devices)} 个输入设备")

            except Exception as e:
                self.log_message(f"❌ PyAudio 设备检测失败: {e}")

        # 方法2: 使用Qt Multimedia API作为补充
        if QT_AUDIO_AVAILABLE:
            try:
                qt_devices = QAudioDeviceInfo.availableDevices(QAudio.AudioInput)
                qt_count = 0

                for qt_device in qt_devices:
                    device_name = qt_device.deviceName()

                    # 检查是否已经通过PyAudio添加过
                    already_exists = any(d['name'] == device_name for d in self.audio_devices)

                    if not already_exists:
                        device_data = {
                            'index': len(self.audio_devices),
                            'name': device_name,
                            'channels': 1,  # Qt默认
                            'sample_rate': 44100,  # Qt默认
                            'api': 'Qt'
                        }

                        self.audio_devices.append(device_data)

                        # 添加到下拉框
                        display_name = f"{device_data['name']} (Qt)"
                        self.device_combo.addItem(display_name, device_data)

                        # 添加到列表
                        item = QListWidgetItem(f"🔊 {display_name}")
                        item.setData(Qt.UserRole, device_data)
                        self.device_list_widget.addItem(item)

                        qt_count += 1

                if qt_count > 0:
                    self.log_message(f"✅ Qt Multimedia 补充检测到 {qt_count} 个设备")

            except Exception as e:
                self.log_message(f"❌ Qt Multimedia 设备检测失败: {e}")

        total_devices = len(self.audio_devices)
        if total_devices == 0:
            self.log_message("⚠️ 未检测到可用的音频输入设备")
            QMessageBox.warning(self, "警告", "未检测到可用的音频输入设备！\n请检查麦克风连接或驱动程序。")
        else:
            self.log_message(f"🎉 总共检测到 {total_devices} 个音频输入设备")

    def toggle_device_monitoring(self):
        """切换设备监听状态"""
        current_device = self.device_combo.currentData()
        if not current_device:
            QMessageBox.warning(self, "警告", "请先选择一个音频设备！")
            return

        device_name = current_device['name']

        if device_name in self.monitor_threads:
            # 停止监听
            self.stop_device_monitoring(device_name)
            self.monitor_btn.setText("开始监听")
            self.log_message(f"⏹️ 停止监听设备: {device_name}")
        else:
            # 开始监听
            self.start_device_monitoring(current_device)
            self.monitor_btn.setText("停止监听")
            self.log_message(f"▶️ 开始监听设备: {device_name}")

    def start_device_monitoring(self, device_info):
        """开始监听指定设备"""
        device_name = device_info['name']

        if device_name in self.monitor_threads:
            return

        # 创建监听线程
        threshold_db = self.threshold_spin.value()
        monitor_thread = AudioMonitorThread(device_info, threshold_db)

        # 连接信号
        monitor_thread.audio_level_changed.connect(self.on_audio_level_changed)
        monitor_thread.error_occurred.connect(self.on_monitor_error)

        # 启动线程
        monitor_thread.start()

        # 保存线程引用
        self.monitor_threads[device_name] = monitor_thread

    def stop_device_monitoring(self, device_name):
        """停止监听指定设备"""
        if device_name in self.monitor_threads:
            thread = self.monitor_threads[device_name]
            thread.stop()
            thread.wait(3000)  # 等待3秒
            del self.monitor_threads[device_name]

    def start_all_monitoring(self):
        """开始监听所有设备"""
        if not self.audio_devices:
            QMessageBox.warning(self, "警告", "没有可用的音频设备！")
            return

        self.log_message("🚀 开始监听所有设备...")

        for device_info in self.audio_devices:
            device_name = device_info['name']
            if device_name not in self.monitor_threads:
                self.start_device_monitoring(device_info)
                self.log_message(f"  ▶️ 启动监听: {device_name}")

    def stop_all_monitoring(self):
        """停止监听所有设备"""
        self.log_message("⏹️ 停止监听所有设备...")

        device_names = list(self.monitor_threads.keys())
        for device_name in device_names:
            self.stop_device_monitoring(device_name)
            self.log_message(f"  ⏹️ 停止监听: {device_name}")

        # 重置按钮状态
        self.monitor_btn.setText("开始监听")

    def on_audio_level_changed(self, device_name, volume, db_level):
        """处理音频电平变化"""
        # 更新音量显示
        self.volume_label.setText(f"音量: {db_level:.1f} dB")

        # 更新进度条 (将-80到0dB映射到0-100)
        progress = max(0, min(100, int((db_level + 80) * 100 / 80)))
        self.volume_bar.setValue(progress)

        # 更新滑块
        self.volume_slider.setValue(int(db_level))

        # 检查是否超过阈值
        threshold = self.threshold_spin.value()
        if db_level > threshold:
            # 触发事件
            self.on_audio_triggered(device_name, db_level)

    def on_audio_triggered(self, device_name, db_level):
        """音频触发事件"""
        timestamp = time.strftime("%H:%M:%S")
        message = f"🔥 [{timestamp}] {device_name}: {db_level:.1f}dB (触发)"
        self.log_message(message)

        # 如果启用了闪避功能
        if self.ducking_enabled.isChecked():
            self.execute_ducking_action(device_name, db_level)

    def execute_ducking_action(self, device_name, db_level):
        """执行闪避动作"""
        ducking_volume = self.ducking_volume_spin.value()
        hold_time = self.hold_time_spin.value()

        self.log_message(f"🎯 执行闪避: {device_name}, 音量调至 {ducking_volume:.2f}, 保持 {hold_time:.1f}s")

        # 这里可以添加实际的闪避逻辑
        # 例如调用OBS WebSocket API或其他音频控制接口

    def on_monitor_error(self, error_message):
        """处理监听错误"""
        self.log_message(f"❌ 监听错误: {error_message}")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        if self.auto_scroll_check.isChecked():
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_message("📝 日志已清空")

    def apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 12px;
                border-radius: 4px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QPushButton:pressed {
                background-color: #21618c;
            }

            QComboBox {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                min-width: 200px;
            }

            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                text-align: center;
                background-color: #ecf0f1;
            }

            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 3px;
            }

            QSlider::groove:horizontal {
                height: 8px;
                background: #bdc3c7;
                border-radius: 4px;
            }

            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }

            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }

            QListWidget {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                alternate-background-color: #f8f9fa;
            }

            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }

            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }

            QSpinBox, QDoubleSpinBox {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                min-width: 80px;
            }

            QCheckBox {
                spacing: 8px;
                font-weight: bold;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }

            QCheckBox::indicator:unchecked {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 3px;
            }

            QCheckBox::indicator:checked {
                background-color: #27ae60;
                border: 2px solid #27ae60;
                border-radius: 3px;
            }
        """)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止所有监听线程
        self.stop_all_monitoring()

        # 等待线程结束
        for thread in list(self.monitor_threads.values()):
            thread.wait(1000)

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("音频监听工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("OBS去重软件")

    # 检查依赖
    missing_deps = []
    if not PYAUDIO_AVAILABLE:
        missing_deps.append("pyaudio")
    if not QT_AUDIO_AVAILABLE:
        missing_deps.append("PyQt5.QtMultimedia")

    if missing_deps:
        print(f"警告: 缺少依赖库: {', '.join(missing_deps)}")
        print("请运行: pip install pyaudio PyQt5")

    # 创建主窗口
    window = AudioMonitorGUI()
    window.show()

    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
