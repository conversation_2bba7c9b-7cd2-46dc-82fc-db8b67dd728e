# 🎬 OBS 去重软件 (OBS Deduplication Controller)

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://www.riverbankcomputing.com/software/pyqt/)
[![OBS Studio](https://img.shields.io/badge/OBS%20Studio-28.0+-orange.svg)](https://obsproject.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)

## 📖 项目简介

OBS 去重软件是一款专为 OBS Studio 直播软件设计的智能去重控制工具。该软件通过 WebSocket 协议与 OBS Studio 进行实时通信，提供多种去重功能，帮助主播在直播过程中自动切换不同的视觉效果和音频效果，避免内容重复，提升直播质量。

### 🎯 核心价值

- **智能去重**：自动切换多种视觉效果，避免直播内容重复
- **实时控制**：通过 WebSocket 与 OBS 实时通信，响应迅速
- **多维度控制**：支持视频、音频、滤镜等多方面去重
- **用户友好**：直观的图形界面，简单易用的操作体验
- **安全可靠**：内置反调试和完整性验证机制

## ✨ 主要功能

### 🎨 视频效果控制
- **模糊滤镜控制**：自动调节模糊半径，创建动态模糊效果
- **颜色滤镜控制**：实时调整色相、饱和度、亮度等参数
- **变换动画控制**：自动生成位置、旋转、缩放动画
- **播放速度控制**：动态调整视频播放速度

### 🎵 音频效果控制
- **智能音频播放器**
  - 支持多种音频格式（MP3、WAV、FLAC等）
  - 音频增益调节功能
  - 音调变化效果
  - 指定音频输出设备
  - 片段播放和完整播放模式

- **音频滤镜控制**
  - 音频均衡器调节
  - 压缩器控制
  - 增益控制
  - 静音控制
  - 音量控制

### 🎭 特殊效果
- **几何形状爆闪播放器**
  - 随机几何形状生成
  - 自定义颜色管理
  - 轮播模式设置
  - 碎片数据范围控制
  - 颜色导入导出功能

### 🔧 插件系统
- **插件去重系统**
  - 支持多种OBS插件自动切换
  - 插件状态实时监控
  - 一键下载缺失插件
  - 独立定时器控制

### ⚡ 一键操作
- **批量启动功能**：一键启动多个去重功能
- **智能状态管理**：实时监控功能运行状态
- **快速停止**：一键停止所有功能

## 🏗️ 技术架构

### 核心技术栈
- **前端界面**：PyQt5 图形界面框架
- **通信协议**：WebSocket 实时通信
- **音频处理**：PyQt5 Multimedia + NumPy + SoundFile
- **系统集成**：Windows API + WMI
- **安全机制**：反调试 + 完整性验证

### 系统架构图
```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   OBS去重软件   │ ◄─────────────► │   OBS Studio    │
│                 │                 │                 │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 主控制界面   │ │                 │ │ 视频源      │ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 音频播放器   │ │                 │ │ 音频源      │ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 爆闪播放器   │ │                 │ │ 滤镜系统    │ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 插件管理器   │ │                 │ │ 插件系统    │ │
│ └─────────────┘ │                 │ └─────────────┘ │
└─────────────────┘                 └─────────────────┘
```

### 核心模块

#### 1. 主控制模块 (`MainWindow`)
- 负责整体界面管理和功能协调
- 处理用户交互和设置管理
- 管理定时器和状态监控

#### 2. OBS通信模块
- WebSocket连接管理
- 请求发送和响应处理
- 实时状态同步

#### 3. 音频处理模块
- 多格式音频文件支持
- 实时音频效果处理
- 设备管理和输出控制

#### 4. 视频控制模块
- 滤镜参数动态调节
- 变换动画生成
- 播放状态监控

#### 5. 安全验证模块
- 反调试检测
- 文件完整性验证
- 激活码验证

## 🚀 安装与使用

### 系统要求
- **操作系统**：Windows 10/11 (64位)
- **OBS Studio**：28.0+ 版本
- **WebSocket插件**：5.0+ 版本
- **Python**：3.8+ (开发环境)

### 安装步骤

#### 方法一：使用预编译版本
1. 下载最新版本的安装包
2. 关闭正在运行的旧版本
3. 运行安装程序
4. 按照向导完成安装
5. 启动软件并激活

#### 方法二：从源码编译
```bash
# 克隆项目
git clone [项目地址]
cd OBS

# 安装依赖
pip install -r requirements.txt

# 编译Cython模块
python setup.py build_ext --inplace

# 运行程序
python main.py
```

### 使用指南

#### 1. 首次启动
1. 启动 OBS Studio
2. 启动 OBS 去重软件
3. 点击"连接OBS"按钮
4. 输入激活码完成激活

#### 2. 基本配置
1. 选择要控制的视频源和音频源
2. 配置各种效果的参数范围
3. 设置定时器间隔
4. 保存配置

#### 3. 功能使用
- **一键启动**：选择需要的功能，点击"启动选中功能"
- **单独控制**：每个功能都有独立的开关和参数调节
- **实时监控**：界面实时显示各功能的运行状态

## 🔧 配置说明

### 视频源配置
```json
{
  "video_source": "摄像头",
  "blur_range": [0, 50],
  "color_range": {
    "hue": [-180, 180],
    "saturation": [0, 200],
    "brightness": [0, 200]
  }
}
```

### 音频配置
```json
{
  "audio_source": "麦克风",
  "audio_folder": "C:/AudioFiles",
  "output_device": "扬声器",
  "gain_range": [-20, 20],
  "pitch_range": [0.5, 2.0]
}
```

### 定时器配置
```json
{
  "blur_interval": [5, 15],
  "color_interval": [3, 10],
  "transform_interval": [8, 20],
  "audio_interval": [10, 30]
}
```

## 🛡️ 安全特性

### 反调试保护
- 检测调试器存在
- 监控调试工具进程
- 检测可疑窗口名称
- 多层安全验证

### 完整性验证
- 文件哈希验证
- 云端哈希对比
- 内存完整性检查
- 权限验证

### 激活系统
- 硬件绑定验证
- 在线激活验证
- 激活码加密存储
- 自动续期机制

## 📊 性能优化

### 内存管理
- 智能内存分配
- 及时释放资源
- 避免内存泄漏
- 优化数据结构

### 通信优化
- WebSocket连接池
- 请求队列管理
- 超时重试机制
- 错误恢复

### 界面优化
- 异步UI更新
- 事件驱动架构
- 响应式设计
- 滚动优化

## 🐛 故障排除

### 常见问题

#### Q: 无法连接到OBS？
A: 
1. 确保OBS Studio正在运行
2. 检查WebSocket插件是否已安装并启用
3. 验证端口设置是否正确
4. 检查防火墙设置

#### Q: 某些功能不工作？
A:
1. 确认OBS版本兼容性
2. 检查视频/音频源名称是否正确
3. 验证滤镜是否已添加到源
4. 查看错误日志

#### Q: 音频播放异常？
A:
1. 检查音频文件格式是否支持
2. 确认音频设备是否可用
3. 验证音频权限设置
4. 重启音频服务

#### Q: 软件启动失败？
A:
1. 以管理员身份运行
2. 检查系统依赖是否完整
3. 验证文件完整性
4. 重新安装软件

### 日志查看
软件运行日志位于：
```
%APPDATA%/OBS去重软件/logs/
```

## 🔄 更新机制

### 自动更新
- 启动时检查更新
- 后台下载更新包
- 自动安装新版本
- 保留用户设置

### 手动更新
1. 在软件内点击"检查更新"
2. 下载最新版本
3. 关闭软件
4. 运行更新程序

## 📞 技术支持

### 联系方式
- **微信客服**：扫描软件内二维码
- **QQ群**：[群号待补充]
- **邮箱支持**：[邮箱待补充]
- **在线文档**：[文档链接待补充]

### 反馈渠道
- 功能建议
- Bug报告
- 使用问题
- 性能优化建议

## 📄 许可证

本项目采用专有许可证，未经授权不得复制、分发或修改。

## 🤝 贡献指南

虽然这是一个专有软件，但我们欢迎：
- Bug报告
- 功能建议
- 文档改进
- 测试反馈

## 📈 版本历史

### v1.3 (2025-01-29)
- ✨ 新增智能音频播放器
- ✨ 新增几何形状爆闪播放器
- ✨ 新增插件去重系统
- ✨ 新增一键启动功能
- 🛠️ 优化断音控制和音频大小控制
- 🎨 全新浅色主题界面
- 🔒 增强安全性和完整性验证

### v1.2 (2024-12-15)
- 新增音频滤镜控制
- 优化WebSocket连接稳定性
- 修复多个已知问题

### v1.1 (2024-11-01)
- 新增变换动画控制
- 改进用户界面
- 增强错误处理

### v1.0 (2024-10-01)
- 初始版本发布
- 基础去重功能
- OBS WebSocket集成

## 🎯 未来规划

### 短期目标
- 更多音频效果器
- 视频滤镜扩展
- 云端设置同步
- 多语言支持

### 长期目标
- 跨平台支持
- 插件生态系统
- AI智能去重
- 云端协作功能

## 💝 致谢

感谢所有用户的反馈和建议，你们的支持是我们持续改进的动力！

特别感谢：
- OBS Studio 开发团队
- PyQt5 社区
- 所有测试用户
- 技术支持团队

---

**OBS去重软件开发团队**  
*让直播更精彩，让内容更丰富*

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

</div> 